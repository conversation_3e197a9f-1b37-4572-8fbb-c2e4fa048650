/// <reference types="vite/client" />

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// 环境变量类型定义
interface ImportMetaEnv {
  readonly VITE_API_BASE_URL: string
  readonly VITE_APP_TITLE: string
  readonly VITE_APP_VERSION: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

// 模块声明 - 解决 TypeScript 模块识别问题
declare module '@/stores/user' {
  export const useUserStore: () => any
}

declare module '@/utils/auth' {
  export const formatRoleName: (role: string) => string
  export const isTokenExpired: (token: string) => boolean
  export const getTokenRemainingTime: (token: string) => number
}
