<!--
  侧边栏菜单组件
  {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-005 基础布局, 创建侧边栏菜单; Principle_Applied: 权限控制菜单;}}
-->

<template>
  <div class="side-menu-container">
    <a-menu
      v-model:selectedKeys="selectedKeys"
      v-model:openKeys="openKeys"
      mode="inline"
      theme="light"
      :inline-collapsed="collapsed"
      class="side-menu"
      @click="handleMenuClick"
    >
      <template v-for="item in menuItems" :key="item.key">
        <a-menu-item
          v-if="!item.children && hasMenuPermission(item)"
          :key="item.key"
          :disabled="item.disabled"
        >
          <component :is="item.icon" />
          <span>{{ item.title }}</span>
        </a-menu-item>

        <a-sub-menu
          v-else-if="item.children && hasMenuPermission(item)"
          :key="item.key"
          :disabled="item.disabled"
        >
          <template #icon>
            <component :is="item.icon" />
          </template>
          <template #title>{{ item.title }}</template>

          <a-menu-item
            v-for="child in item.children"
            :key="child.key"
            :disabled="child.disabled"
            v-show="hasMenuPermission(child)"
          >
            <component :is="child.icon" />
            <span>{{ child.title }}</span>
          </a-menu-item>
        </a-sub-menu>
      </template>
    </a-menu>

    <!-- 用户管理区域 - 放在菜单底部 -->
    <div class="user-management" :class="{ collapsed }">
      <div v-if="!collapsed" class="user-info-item" @click="handleUserManagement">
        <a-avatar
          :size="40"
          :src="userStore.user?.avatarUrl"
          class="user-avatar"
        >
          {{ userStore.user?.realName?.charAt(0) }}
        </a-avatar>
        <div class="user-details">
          <div class="user-name">{{ userStore.user?.realName }}</div>
          <div class="user-title">用户管理</div>
        </div>
      </div>

      <!-- 折叠状态下只显示头像 -->
      <div v-if="collapsed" class="user-info-collapsed" @click="handleUserManagement">
        <a-avatar
          :size="32"
          :src="userStore.user?.avatarUrl"
          class="user-avatar"
        >
          {{ userStore.user?.realName?.charAt(0) }}
        </a-avatar>
      </div>

      <!-- 折叠展开按钮 -->
      <a-button
        type="text"
        @click="props.toggleCollapsed"
        class="collapse-trigger"
        :class="{ collapsed }"
        size="small"
      >
        <template #icon>
          <MenuUnfoldOutlined v-if="collapsed" />
          <MenuFoldOutlined v-else />
        </template>
      </a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  ShoppingOutlined,
  ScheduleOutlined,
  DollarOutlined,
  CloudOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined
} from '@ant-design/icons-vue'
import { useUserStore } from '@/stores/user'

interface MenuItem {
  key: string
  title: string
  icon: any
  path?: string
  permissions?: string[]
  roles?: string[]
  disabled?: boolean
  children?: MenuItem[]
}

interface Props {
  collapsed: boolean
  toggleCollapsed: () => void
}

const props = defineProps<Props>()

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 菜单项配置
const menuItems: MenuItem[] = [
  {
    key: 'orders',
    title: '订单管理',
    icon: ShoppingOutlined,
    path: '/orders',
    permissions: ['order:view']
  },
  {
    key: 'production',
    title: '生产计划',
    icon: ScheduleOutlined,
    path: '/production',
    permissions: ['production:view']
  },
  {
    key: 'salary',
    title: '工资管理',
    icon: DollarOutlined,
    path: '/salary',
    permissions: ['salary:view']
  },
  {
    key: 'digital',
    title: '数字空间',
    icon: CloudOutlined,
    path: '/digital',
    permissions: ['digital:view']
  }
]

// 当前选中的菜单项
const selectedKeys = ref<string[]>([])
// 当前展开的子菜单
const openKeys = ref<string[]>([])

// 根据当前路由设置选中的菜单项
const updateSelectedKeys = () => {
  const currentPath = route.path
  
  // 查找匹配的菜单项
  const findMenuKey = (items: MenuItem[], path: string): string | null => {
    for (const item of items) {
      if (item.path === path) {
        return item.key
      }
      if (item.children) {
        const childKey = findMenuKey(item.children, path)
        if (childKey) {
          // 如果是子菜单项，同时展开父菜单
          if (!openKeys.value.includes(item.key)) {
            openKeys.value.push(item.key)
          }
          return childKey
        }
      }
    }
    return null
  }
  
  const menuKey = findMenuKey(menuItems, currentPath)
  if (menuKey) {
    selectedKeys.value = [menuKey]
  }
}

// 检查菜单权限
const hasMenuPermission = (item: MenuItem): boolean => {
  // 如果没有权限要求，默认显示
  if (!item.permissions && !item.roles) {
    return true
  }
  
  // 检查角色权限
  if (item.roles && item.roles.length > 0) {
    const hasRole = userStore.hasRoles(item.roles)
    if (!hasRole) {
      return false
    }
  }
  
  // 检查功能权限
  if (item.permissions && item.permissions.length > 0) {
    return userStore.hasPermissions(item.permissions)
  }
  
  return true
}

// 处理菜单点击
const handleMenuClick = ({ key }: { key: string }) => {
  // 查找对应的菜单项
  const findMenuItem = (items: MenuItem[], targetKey: string): MenuItem | null => {
    for (const item of items) {
      if (item.key === targetKey) {
        return item
      }
      if (item.children) {
        const child = findMenuItem(item.children, targetKey)
        if (child) return child
      }
    }
    return null
  }

  const menuItem = findMenuItem(menuItems, key)
  if (menuItem && menuItem.path) {
    router.push(menuItem.path)
  }
}

// 处理用户管理点击
const handleUserManagement = () => {
  // 检查用户是否有权限访问用户设置页面
  if (userStore.hasPermission('user:view')) {
    router.push('/users')
  }
  // 如果没有权限，不做任何操作（不跳转）
}

// 监听路由变化
watch(route, updateSelectedKeys, { immediate: true })

// 计算可见的菜单项（用于性能优化）
const visibleMenuItems = computed(() => {
  return menuItems.filter(item => hasMenuPermission(item))
})
</script>

<style scoped>
.side-menu-container {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 100%;
}

.side-menu {
  flex: 1;
  border-right: none;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 0;
  height: 0;
  padding-bottom: 80px;
}

.side-menu::-webkit-scrollbar {
  width: 6px;
}

.side-menu::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.side-menu::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.side-menu::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 菜单项样式调整 */
:deep(.ant-menu-item) {
  margin: 4px 8px;
  border-radius: 6px;
  height: 40px;
  line-height: 40px;
}

:deep(.ant-menu-submenu) {
  margin: 4px 8px;
  border-radius: 6px;
}

:deep(.ant-menu-submenu-title) {
  height: 40px;
  line-height: 40px;
  border-radius: 6px;
}

:deep(.ant-menu-item-selected) {
  background-color: #e6f7ff;
  color: #1890ff;
}

:deep(.ant-menu-item:hover) {
  background-color: #f5f5f5;
}

:deep(.ant-menu-submenu-title:hover) {
  background-color: #f5f5f5;
}

/* 折叠状态下的样式 */
:deep(.ant-menu-inline-collapsed .ant-menu-item) {
  padding: 0 20px;
}

:deep(.ant-menu-inline-collapsed .ant-menu-submenu) {
  padding: 0;
}

/* 用户管理区域样式 */
.user-management {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  border-top: 1px solid #f0f0f0;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.user-management.collapsed {
  flex-direction: column;
  gap: 8px;
  padding: 12px 8px;
}

.user-info-item {
  display: flex;
  align-items: center;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.3s;
  cursor: pointer;
  flex: 1;
}

.user-info-item:hover {
  background-color: #f5f5f5;
}

.user-info-collapsed {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.3s;
  cursor: pointer;
  width: 100%;
}

.user-info-collapsed:hover {
  background-color: #f5f5f5;
}

.collapse-trigger {
  font-size: 16px;
  color: #8c8c8c;
  flex-shrink: 0;
  margin-left: 8px;
}

.collapse-trigger.collapsed {
  margin-left: 0;
  width: 100%;
  display: flex;
  justify-content: center;
}

.collapse-trigger:hover {
  color: #1890ff;
}

.user-avatar {
  background-color: #1890ff;
  flex-shrink: 0;
}

.user-details {
  margin-left: 12px;
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-title {
  font-size: 12px;
  color: #8c8c8c;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
