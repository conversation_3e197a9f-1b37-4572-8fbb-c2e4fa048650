<!--
  主布局组件
  {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-005 基础布局, 创建主布局组件; Principle_Applied: 布局组件化设计;}}
-->

<template>
  <a-layout class="main-layout">
    <!-- 侧边栏 -->
    <a-layout-sider
      v-model:collapsed="collapsed"
      :trigger="null"
      collapsible
      :width="240"
      :collapsed-width="80"
      class="layout-sider"
    >
      <!-- 企业信息区域 -->
      <div class="enterprise-info">
        <div class="enterprise-logo">
          <img
            v-if="userStore.user?.enterprise?.logoUrl"
            :src="userStore.user.enterprise.logoUrl"
            :alt="userStore.user.enterprise.name"
            class="logo-img"
          />
          <div v-else class="logo-placeholder">
            <BankOutlined />
          </div>
        </div>
        <div v-if="!collapsed" class="enterprise-name">
          {{ userStore.user?.enterprise?.name || '默认企业' }}
        </div>
      </div>

      <!-- 导航菜单 -->
      <div class="menu-container">
        <SideMenu :collapsed="collapsed" :toggle-collapsed="toggleCollapsed" />
      </div>
    </a-layout-sider>

    <!-- 主内容区域 -->
    <a-layout :class="['layout-content', { collapsed }]">
      <!-- 页面内容 -->
      <a-layout-content class="page-content">
        <router-view v-slot="{ Component }">
          <transition name="fade" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { BankOutlined } from '@ant-design/icons-vue'
import { useUserStore } from '@/stores/user'
import SideMenu from './SideMenu.vue'

const route = useRoute()
const userStore = useUserStore()

// 侧边栏折叠状态
const collapsed = ref(false)

// 切换侧边栏折叠状态
const toggleCollapsed = () => {
  collapsed.value = !collapsed.value
}

// 监听路由变化，自动收起移动端菜单
watch(route, () => {
  if (window.innerWidth <= 768) {
    collapsed.value = true
  }
})
</script>

<style scoped>
.main-layout {
  min-height: 100vh;
}

.layout-sider {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 100;
  box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #fff !important;
}

:deep(.ant-layout-sider) {
  background: #fff !important;
}

.enterprise-info {
  padding: 16px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
}

.enterprise-logo {
  margin-bottom: 8px;
}

.logo-img {
  width: 40px;
  height: 40px;
  border-radius: 4px;
}

.logo-placeholder {
  width: 40px;
  height: 40px;
  background: #1890ff;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  margin: 0 auto;
}

.enterprise-name {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.menu-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
  height: calc(100vh - 80px);
}



.layout-content {
  margin-left: 240px;
  transition: margin-left 0.2s;
}

.layout-content.collapsed {
  margin-left: 80px;
}

.page-content {
  min-height: 100vh;
  background: #f0f2f5;
}

/* 页面切换动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .layout-sider {
    transform: translateX(-100%);
    transition: transform 0.3s;
  }
  
  .layout-sider:not(.ant-layout-sider-collapsed) {
    transform: translateX(0);
  }
  
  .layout-content {
    margin-left: 0 !important;
  }
  
  .enterprise-name,
  .user-details {
    display: none;
  }
}
</style>
