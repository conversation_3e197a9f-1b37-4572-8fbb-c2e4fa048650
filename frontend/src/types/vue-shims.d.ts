/**
 * Vue 组件类型声明
 */

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// 解决模块导入问题的类型声明
declare module '@/stores/user' {
  export const useUserStore: () => any
}

declare module '@/utils/auth' {
  export const formatRoleName: (role: string) => string
  export const isTokenExpired: (token: string) => boolean
  export const getTokenRemainingTime: (token: string) => number
}
