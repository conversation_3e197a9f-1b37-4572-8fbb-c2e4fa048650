/**
 * 用户状态管理
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-004 前端框架, 创建用户状态管理; Principle_Applied: 状态管理模式;}}
 */

import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import { authApi } from '@/api/auth'
import type { LoginRequest, LoginResponse, UserProfile } from '@/types/auth'

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref<UserProfile | null>(null)
  const accessToken = ref<string>('')
  const refreshToken = ref<string>('')
  const permissions = ref<string[]>([])
  const roles = ref<string[]>([])
  const loading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!accessToken.value && !!user.value)
  const isAdmin = computed(() => roles.value.includes('ENTERPRISE_ADMIN'))
  const isSuperAdmin = computed(() => roles.value.includes('SUPER_ADMIN'))

  // 方法
  const login = async (loginData: LoginRequest): Promise<void> => {
    loading.value = true
    try {
      const response = await authApi.login(loginData)
      const { user: userData, tokens, permissions: userPermissions, roles: userRoles } = response

      // 保存用户信息
      user.value = userData
      accessToken.value = tokens.accessToken
      refreshToken.value = tokens.refreshToken
      permissions.value = userPermissions
      roles.value = userRoles

      // 保存到本地存储
      localStorage.setItem('accessToken', tokens.accessToken)
      localStorage.setItem('refreshToken', tokens.refreshToken)
      localStorage.setItem('user', JSON.stringify(userData))
      localStorage.setItem('permissions', JSON.stringify(userPermissions))
      localStorage.setItem('roles', JSON.stringify(userRoles))
    } finally {
      loading.value = false
    }
  }

  const logout = async (): Promise<void> => {
    try {
      if (accessToken.value) {
        await authApi.logout()
      }
    } catch (error) {
      console.warn('登出API调用失败:', error)
    } finally {
      // 清除状态
      user.value = null
      accessToken.value = ''
      refreshToken.value = ''
      permissions.value = []
      roles.value = []

      // 清除本地存储
      localStorage.removeItem('accessToken')
      localStorage.removeItem('refreshToken')
      localStorage.removeItem('user')
      localStorage.removeItem('permissions')
      localStorage.removeItem('roles')
    }
  }

  const refreshAccessToken = async (): Promise<boolean> => {
    if (!refreshToken.value) {
      return false
    }

    try {
      const response = await authApi.refreshToken({ refreshToken: refreshToken.value })
      accessToken.value = response.accessToken
      localStorage.setItem('accessToken', response.accessToken)
      return true
    } catch (error) {
      console.error('刷新令牌失败:', error)
      await logout()
      return false
    }
  }

  const getUserProfile = async (): Promise<void> => {
    try {
      const profile = await authApi.getProfile()
      user.value = profile
      permissions.value = profile.permissions
      roles.value = profile.roles

      // 更新本地存储
      localStorage.setItem('user', JSON.stringify(profile))
      localStorage.setItem('permissions', JSON.stringify(profile.permissions))
      localStorage.setItem('roles', JSON.stringify(profile.roles))
    } catch (error) {
      console.error('获取用户信息失败:', error)
      await logout()
    }
  }

  const initializeAuth = async (): Promise<void> => {
    // 从本地存储恢复状态
    const storedToken = localStorage.getItem('accessToken')
    const storedRefreshToken = localStorage.getItem('refreshToken')
    const storedUser = localStorage.getItem('user')
    const storedPermissions = localStorage.getItem('permissions')
    const storedRoles = localStorage.getItem('roles')

    if (storedToken && storedUser) {
      accessToken.value = storedToken
      refreshToken.value = storedRefreshToken || ''
      user.value = JSON.parse(storedUser)
      permissions.value = storedPermissions ? JSON.parse(storedPermissions) : []
      roles.value = storedRoles ? JSON.parse(storedRoles) : []

      // 验证令牌有效性
      try {
        await authApi.verifyToken()
        // 如果令牌有效，获取最新的用户信息
        await getUserProfile()
      } catch (error) {
        // 令牌无效，尝试刷新
        const refreshed = await refreshAccessToken()
        if (refreshed) {
          await getUserProfile()
        }
      }
    }
  }

  const hasPermission = (permission: string): boolean => {
    return isSuperAdmin.value || permissions.value.includes(permission)
  }

  const hasPermissions = (requiredPermissions: string[]): boolean => {
    if (isSuperAdmin.value) return true
    return requiredPermissions.every(permission => permissions.value.includes(permission))
  }

  const hasRole = (role: string): boolean => {
    return roles.value.includes(role)
  }

  const hasRoles = (requiredRoles: string[]): boolean => {
    return requiredRoles.some(role => roles.value.includes(role))
  }

  const hasAnyPermission = (requiredPermissions: string[]): boolean => {
    if (isSuperAdmin.value) return true
    return requiredPermissions.some(permission => permissions.value.includes(permission))
  }

  const updateUserInfo = (userInfo: Partial<UserProfile>): void => {
    if (user.value) {
      user.value = { ...user.value, ...userInfo }
      localStorage.setItem('user', JSON.stringify(user.value))
    }
  }

  return {
    // 状态
    user: readonly(user),
    accessToken: readonly(accessToken),
    refreshToken: readonly(refreshToken),
    permissions: readonly(permissions),
    roles: readonly(roles),
    loading: readonly(loading),

    // 计算属性
    isAuthenticated,
    isAdmin,
    isSuperAdmin,

    // 方法
    login,
    logout,
    refreshAccessToken,
    getUserProfile,
    initializeAuth,
    hasPermission,
    hasPermissions,
    hasRole,
    hasRoles,
    hasAnyPermission,
    updateUserInfo
  }
})
