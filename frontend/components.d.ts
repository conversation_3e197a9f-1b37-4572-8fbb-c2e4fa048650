/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AAvatar: typeof import('ant-design-vue/es')['Avatar']
    AButton: typeof import('ant-design-vue/es')['Button']
    ACard: typeof import('ant-design-vue/es')['Card']
    ACol: typeof import('ant-design-vue/es')['Col']
    ALayout: typeof import('ant-design-vue/es')['Layout']
    ALayoutContent: typeof import('ant-design-vue/es')['LayoutContent']
    ALayoutSider: typeof import('ant-design-vue/es')['LayoutSider']
    AMenu: typeof import('ant-design-vue/es')['Menu']
    AMenuItem: typeof import('ant-design-vue/es')['MenuItem']
    ARow: typeof import('ant-design-vue/es')['Row']
    AStatistic: typeof import('ant-design-vue/es')['Statistic']
    ASubMenu: typeof import('ant-design-vue/es')['SubMenu']
    ATag: typeof import('ant-design-vue/es')['Tag']
    BreadcrumbNav: typeof import('./src/components/Layout/BreadcrumbNav.vue')['default']
    MainLayout: typeof import('./src/components/Layout/MainLayout.vue')['default']
    PermissionWrapper: typeof import('./src/components/Permission/PermissionWrapper.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SideMenu: typeof import('./src/components/Layout/SideMenu.vue')['default']
  }
}
